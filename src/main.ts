import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 导入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入迁移的样式文件 - 按照参考项目的顺序
import '@/styles/base.scss'

// 权限指令
import { setupPermissionDirectives } from '@/components/Permission'

// 创建应用实例
const app = createApp(App)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(ElementPlus)
app.use(router)
app.use(store)

// 注册权限指令
setupPermissionDirectives(app)

// 挂载应用
app.mount('#app')
