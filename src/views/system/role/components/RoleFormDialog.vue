<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑角色' : '新增角色'"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入角色名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色编码" prop="code">
            <el-input v-model="formData.code" placeholder="请输入角色编码" :disabled="isEdit" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色级别" prop="level">
            <el-select v-model="formData.level" placeholder="请选择角色级别" style="width: 100%">
              <el-option label="超级管理员" :value="1" :disabled="!canCreateSuperAdmin" />
              <el-option label="管理员" :value="2" />
              <el-option label="普通用户" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据权限" prop="dataScope">
            <el-select
              v-model="formData.dataScope"
              placeholder="请选择数据权限"
              style="width: 100%"
            >
              <el-option label="全部数据" :value="DataScope.ALL" />
              <el-option label="本部门及下级" :value="DataScope.DEPT_AND_SUB" />
              <el-option label="本部门" :value="DataScope.DEPT" />
              <el-option label="仅本人" :value="DataScope.SELF" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="角色状态" prop="status" v-if="isEdit">
        <el-radio-group v-model="formData.status">
          <el-radio :label="RoleStatus.ENABLE">启用</el-radio>
          <el-radio :label="RoleStatus.DISABLE">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
        />
      </el-form-item>

      <!-- 权限配置 -->
      <el-form-item label="权限配置" v-if="!isEdit">
        <el-alert
          title="新增角色时可以先创建基本信息，权限配置可以在创建后进行详细设置"
          type="info"
          :closable="false"
          show-icon
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/store/modules/system'
import type { Role, CreateRoleRequest, UpdateRoleRequest } from '@/types/system'
import { RoleStatus, DataScope } from '@/types/system'

interface Props {
  visible: boolean
  roleData?: Role | null
  isEdit?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  roleData: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

// Store
const systemStore = useSystemStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

const formData = reactive({
  name: '',
  code: '',
  description: '',
  level: 3 as number,
  dataScope: DataScope.DEPT as DataScope,
  status: RoleStatus.ENABLE
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
})

// 是否可以创建超级管理员角色（仅超级管理员可以创建）
const canCreateSuperAdmin = computed(() => {
  // 这里可以根据当前用户权限判断
  return true // 暂时允许，实际应该检查当前用户权限
})

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '角色编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  level: [{ required: true, message: '请选择角色级别', trigger: 'change' }],
  dataScope: [{ required: true, message: '请选择数据权限', trigger: 'change' }],
  description: [{ max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }]
}))

// 方法
const initFormData = () => {
  if (props.isEdit && props.roleData) {
    Object.assign(formData, {
      name: props.roleData.name,
      code: props.roleData.code,
      description: props.roleData.description || '',
      level: props.roleData.level,
      dataScope: props.roleData.dataScope,
      status: props.roleData.status
    })
  } else {
    Object.assign(formData, {
      name: '',
      code: '',
      description: '',
      level: 3,
      dataScope: DataScope.DEPT,
      status: RoleStatus.ENABLE
    })
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    if (props.isEdit && props.roleData) {
      // 编辑角色
      const updateData: UpdateRoleRequest = {
        name: formData.name,
        description: formData.description || undefined,
        level: formData.level,
        dataScope: formData.dataScope,
        status: formData.status
      }

      await systemStore.updateRole(props.roleData.id, updateData)
    } else {
      // 新增角色
      const createData: CreateRoleRequest = {
        name: formData.name,
        code: formData.code,
        description: formData.description || undefined,
        level: formData.level,
        permissionIds: [], // 新增角色时默认无权限，后续可以通过权限配置添加
        menuIds: [], // 新增角色时默认无菜单权限，后续可以通过权限配置添加
        dataScope: formData.dataScope
      }

      await systemStore.createRole(createData)
    }

    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

// 监听对话框显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initFormData()
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  &:focus-within {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-textarea__inner) {
  &:focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-alert) {
  margin: 0;
}
</style>
