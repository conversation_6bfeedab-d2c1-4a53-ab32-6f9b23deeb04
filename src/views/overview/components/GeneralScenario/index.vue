<script setup lang="ts">
import GSItem from './GSItem.vue'

defineOptions({
  name: 'GeneralScenario'
})

const data = [
  {
    icon: 'icon1',
    text: '场景1'
  },
  {
    icon: 'icon2',
    text: '场景2'
  },
  {
    icon: 'icon3',
    text: '场景3'
  },
  {
    icon: 'icon4',
    text: '场景4'
  },
  {
    icon: 'icon5',
    text: '场景5'
  },
  {
    icon: 'icon6',
    text: '场景6'
  }
]
</script>

<template>
  <div class="general-scenario">
    <template v-for="item of data" :key="item">
      <GSItem :info="item"></GSItem>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.general-scenario {
  width: 100%;
  max-height: 100%;
  overflow: auto;
  display: grid;
  grid-template-columns: repeat(3, minmax(174px, 1fr));
  gap: 24px;
}
</style>
