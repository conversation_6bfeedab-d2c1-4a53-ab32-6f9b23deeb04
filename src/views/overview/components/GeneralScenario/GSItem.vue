<script setup lang="ts">
defineOptions({
  name: 'G<PERSON>tem'
})

const { info } = defineProps<{
  info: {
    icon: string
    text: string
  }
}>()
</script>

<template>
  <div class="gs-item">
    <div class="gs-icon"></div>
    <div class="gs-info">{{ info.text }}</div>
  </div>
</template>

<style lang="scss" scoped>
.gs-item {
  min-width: 173px;
  height: 64px;
  background: #eaf3ff;
  border-radius: 8px 8px 8px 8px;

  padding: 12px 16px;
  display: flex;
  align-items: center;

  .gs-icon {
    width: 40px;
    height: 40px;
    background: #0b457f;
    border-radius: 5px 5px 5px 5px;
  }
  .gs-info {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    margin-left: 8px;
  }
}
</style>
