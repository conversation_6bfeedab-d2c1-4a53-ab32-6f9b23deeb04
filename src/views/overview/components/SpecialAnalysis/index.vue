<script setup lang="ts">
import SAItem from './SaItem.vue'
defineOptions({
  name: 'SpecialAnalysis'
})

const data = [
  {
    logo: 'icon1',
    text: '2025年7月深蓝汽车声量分析'
  },
  {
    logo: 'icon1',
    text: '2025年7月深蓝汽车声量分析'
  },
  {
    logo: 'icon1',
    text: '2025年7月深蓝汽车声量分析'
  },
  {
    logo: 'icon1',
    text: '2025年7月深蓝汽车声量分析'
  },
  {
    logo: 'icon1',
    text: '2025年7月深蓝汽车声量分析'
  },
  {
    logo: 'icon1',
    text: '2025年7月深蓝汽车声量分析'
  }
]
</script>

<template>
  <div class="special-analysis">
    <SAItem v-for="(item, index) of data" :key="index" :info="item" :active="index === 0"></SAItem>
  </div>
</template>

<style lang="scss" scoped>
.special-analysis {
  width: 100%;
  max-height: 100%;
  overflow: auto;
}
</style>
