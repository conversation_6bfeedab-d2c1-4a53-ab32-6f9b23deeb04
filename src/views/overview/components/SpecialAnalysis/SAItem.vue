<script setup lang="ts">
defineOptions({
  name: 'SAItem'
})

const { info, active = false } = defineProps<{
  info: {
    logo: string
    text: string
  }
  active?: boolean
}>()
</script>

<template>
  <div class="sa-item" :class="{ active: active }">
    <div class="sa-logo"></div>
    <div class="sa-info">{{ info.text }}</div>
    <div class="sa-btn mr-10" v-if="active">待查看</div>
  </div>
</template>

<style lang="scss" scoped>
.sa-item {
  background: #f2f3f5;
  border-radius: 4px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  cursor: pointer;

  &.active {
    background: #eaf3ff;

    .sa-info {
      font-weight: 500;
    }
  }

  & + .sa-item {
    margin-top: 16px;
  }

  .sa-logo {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }
  .sa-info {
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 20px;
    flex: 1;
  }
  .sa-btn {
    font-weight: 500;
    font-size: 14px;
    color: #1677ff;
    line-height: 18px;
  }
}
</style>
