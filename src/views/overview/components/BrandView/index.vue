<script setup lang="ts">
import BrandMentionTrend from './BrandMentionTrend.vue'

defineOptions({
  name: 'BrandView'
})
</script>

<template>
  <div class="brand-view">
    <template v-for="item of 6" :key="item">
      <div class="bv-item" :class="{ default: item !== 1, active: item === 1 }">
        <div class="flex-between">
          <div class="flex-y-center">
            <div class="bvi-logo"></div>
            <div class="bvi-title ml-8">长安汽车集团</div>
          </div>
          <div class="bvi-link">
            <el-icon :size="14"><TopRight /></el-icon>
          </div>
        </div>
        <div class="text-body text-tertiary mt-16">负面率</div>
        <div class="flex-y-center">
          <div class="text-h3">56.42%</div>
          <div class="tag-def ml-6">+15.51%</div>
        </div>

        <div class="flex mt-16">
          <div class="mr-6 w-60">
            <div class="text-body text-tertiary">提及量</div>
            <div class="text-h4">28.48w</div>
          </div>
          <BrandMentionTrend></BrandMentionTrend>
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.brand-view {
  @extend .flex-y-center;
  @extend .gap-16;
  .bv-item {
    &.default {
      width: 193px;
      height: 184px;
    }
    &.active {
      width: 211px;
      height: 204px;
    }
    padding: 16px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 1px 1px 0px rgba(10, 13, 18, 0.05);
    border-radius: 8px 8px 8px 8px;

    .bvi-logo {
      width: 28px;
      height: 28px;
      background: #0b457f;
      border-radius: 5px 5px 5px 5px;
    }
    .bvi-title {
      @extend .text-body;
      @extend .font-600;
      color: #1d252f;
    }
    .bvi-link {
      width: 28px;
      height: 28px;
      background: #f2f3f5;
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.5);
      @extend .flex-center;
    }
  }
}
</style>
