<script setup lang="ts">
import BrandView from './components/BrandView/index.vue'
import GeneralScenario from './components/GeneralScenario/index.vue'
import SpecialAnalysis from './components/SpecialAnalysis/index.vue'
import FCard from '@/components/FCard/index.vue'

defineOptions({
  name: 'Overview'
})
</script>

<template>
  <div>
    <el-row>
      <el-col :span="16">
        <BrandView></BrandView>
      </el-col>
    </el-row>

    <el-row class="mt-24" :gutter="24">
      <el-col :span="8">
        <FCard :title="'通用场景'" :height="'364px'">
          <GeneralScenario></GeneralScenario>
        </FCard>
      </el-col>
      <el-col :span="8">
        <FCard :title="'专项分析'" :height="'364px'">
          <SpecialAnalysis></SpecialAnalysis>
        </FCard>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped></style>
