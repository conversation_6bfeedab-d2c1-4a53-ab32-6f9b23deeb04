//设计规范变量
@use './variables' as *;
// 工具类
@use './utilities' as *;
// Element Plus 重置样式
@use './element-plus-reset' as *;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  font-size: 14px;
  line-height: 1;
  font-family: var(--font-family);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;

  &:hover {
    background: var(--el-color-primary-light-3);
  }
}

/* 全局 tooltip 样式，可复用 */
.public-tooltip-div {
  padding-top: 11px;
  padding-left: 17px;
  padding-right: 15px;

  .axis-name {
    margin-bottom: 10px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
  }
  .each-series {
    margin-bottom: 10px;
    color: rgba(0, 0, 0, 0.95);
    font-weight: 500;
    .each-series-name {
      width: 100px;
      margin-right: 10px;
      display: inline-block;
      text-align: left;
    }
    .each-series-value {
      display: inline-block;
      min-width: 40px;
      text-align: right;
    }
  }
}
.public-tooltip-click-tips {
  padding: 8px 0;
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  color: rgba(135, 135, 135, 0.85);
}
