import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const olderRputer = [
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: { title: '首页', icon: 'House' }
  },
  {
    path: '/system',
    name: 'System',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: '/system/user',
        name: 'UserManagement',
        component: () => import('@/views/system/user/index.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: '/system/role',
        name: 'RoleManagement',
        component: () => import('@/views/system/role/index.vue'),
        meta: { title: '角色管理', icon: 'UserFilled' }
      }
    ]
  },
  {
    path: '/data',
    name: 'Data',
    meta: { title: '数据管理', icon: 'DataAnalysis' },
    children: [
      {
        path: '/data/analysis',
        name: 'DataAnalysis',
        component: () => import('@/views/data/analysis/index.vue'),
        meta: { title: '数据分析', icon: 'TrendCharts' }
      },
      {
        path: '/data/analysis/chart-demo',
        name: 'ChartDemo',
        component: () => import('@/views/data/analysis/chart-demo.vue'),
        meta: { title: '图表演示', icon: 'PieChart' }
      },
      {
        path: '/data/analysis/bar-or-line-chart-test',
        name: 'BarOrLineChartTest',
        component: () => import('@/views/data/analysis/bar-or-line-chart-test.vue'),
        meta: { title: 'BarOrLineChart测试', icon: 'Histogram' }
      },

      {
        path: '/data/report',
        name: 'DataReport',
        component: () => import('@/views/data/report/index.vue'),
        meta: { title: '数据报表', icon: 'Document' }
      },
      {
        path: '/data/dictionary',
        name: 'DictionaryManagement',
        component: () => import('@/views/data/dictionary/index.vue'),
        meta: { title: '数据字典', icon: 'CollectionTag' }
      }
    ]
  }
]

// 基础路由
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/overview',
    children: [
      {
        path: '/overview',
        name: 'overview',
        component: () => import('@/views/overview/index.vue'),
        meta: { title: '总览', icon: 'House' }
      },
      ...olderRputer
    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置标题
  document.title = to.meta.title ? `${to.meta.title} - 管理系统` : '管理系统'

  // 不需要登录的页面（白名单）
  const whiteList = ['/login', '/404', '/home']

  if (whiteList.includes(to.path)) {
    // 在白名单，直接放行
    next()
  } else {
    // TODO: 这里可以添加登录验证逻辑
    // 目前暂时放行所有页面
    next()
  }
})

export default router
