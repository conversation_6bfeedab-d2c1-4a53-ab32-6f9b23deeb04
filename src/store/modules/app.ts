import { defineStore } from 'pinia'

// 定义用户信息接口
interface UserInfo {
  id?: number
  userName: string
  userAccount: string
  email?: string
  avatar?: string
}

export interface AppState {
  isCollapse: boolean
  user: UserInfo
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    isCollapse: true,
    user: {
      id: 1,
      userName: '管理员',
      userAccount: 'admin'
    }
  }),

  getters: {
    userInfo: state => state.user
  },

  actions: {
    setIsCollapse(isCollapse: boolean) {
      this.isCollapse = isCollapse
    },

    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    },

    setUser(user: Partial<UserInfo>) {
      this.user = { ...this.user, ...user }
    },

    logout() {
      this.user = {
        id: 0,
        userName: '',
        userAccount: ''
      }
    }
  }
})
