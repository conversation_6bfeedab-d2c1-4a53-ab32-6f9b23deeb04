<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header__left">
      <div class="page-title">VoC总览</div>

      <div>
        <el-button-group size="large">
          <el-button>近1天</el-button>
          <el-button> 近7天 </el-button>
          <el-button> 近30天 </el-button>
          <el-button> 近一年 </el-button>
        </el-button-group>

        <el-date-picker type="date" placeholder="自定义时间" size="large" class="ml-8" />
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="header__right">
      <div class="flex gap-16">
        <div class="icon-container">
          <el-icon :size="24"><Search /></el-icon>
        </div>
        <div class="icon-container">
          <el-icon :size="24"><Search /></el-icon>
        </div>
        <div class="icon-container">
          <el-icon :size="24"><Search /></el-icon>
        </div>
      </div>
      <!-- 用户信息 -->
      <el-dropdown trigger="click" @command="handleUserCommand">
        <div class="user-info ml-21">
          <div class="avatar ml-4">U</div>
          <span class="user-name">{{ userInfo.userName }}</span>
          <el-icon class="user-arrow mr-12">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'LayoutHeader'
})

const router = useRouter()
const appStore = useAppStore()

const userInfo = computed(() => appStore.userInfo)

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        appStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.header {
  @extend .flex-x-between-x-center;
  height: var(--header-height);
  background: var(--bg-regular);
  padding: 0 32px;
  border-bottom: 1px solid var(--border-dark);

  &__left {
    @extend .flex-y-center;
    gap: 24px;
  }

  &__right {
    @extend .flex-y-center;
  }
}

.user-info {
  background: #f8f8f8;
  border-radius: 24px 24px 24px 24px;
  border: 1px solid #d5d7da;
  line-height: 40px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  @extend .text-h4;
  @extend .font-500;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .avatar {
    width: 32px;
    height: 32px;
    background: #4a9eff;
    border-radius: 50%;
    @extend .flex-center;
    @extend .font-600;
    color: #fff;
  }

  .user-name {
    color: #1b212d;
  }

  .user-arrow {
    color: #999999;
    font-size: 20px;
  }
}

:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      margin-right: 0;
    }
  }
}
</style>
