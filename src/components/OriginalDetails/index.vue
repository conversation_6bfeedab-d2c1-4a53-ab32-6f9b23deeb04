<template>
  <div class="original-details">
    <!-- 组件标题和数据统计 -->
    <div class="header">
      <h3 v-if="title" class="title">{{ title }}</h3>
      <div class="data-count">共查询到 {{ total }} 条数据</div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="data"
        stripe
        size="default"
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
          :index="getTableIndex"
        />

        <!-- 内容列 -->
        <el-table-column label="内容" align="left">
          <template #default="{ row }">
            <div class="content-cell">
              <!-- 标题 -->
              <div class="content-title">
                {{ getContentTitle(row) }}
              </div>

              <!-- 详细内容 -->
              <div class="content-detail">
                {{ getContentDetail(row) }}
              </div>

              <!-- 文本信息 -->
              <TextInfos :data="row" :text-type="getTextType(row.dataSourceName)" />

              <!-- 关键词标签 -->
              <KeywordTags
                :index-type-name="indexTypeName"
                :data="row.analysisResult"
                :show-title="true"
              />
            </div>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <!-- 查看用户详情 -->
              <el-button
                v-if="row.oneId"
                type="primary"
                size="small"
                @click="handleUserDetail(row)"
              >
                {{ row.user || row.name || '查看用户详情' }}
              </el-button>
              <span v-else class="no-user">-</span>

              <el-divider direction="vertical" />

              <!-- 查看原文 -->
              <el-button type="primary" size="small" @click="handleViewDetail(row)">
                查看原文
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPageValue"
          v-model:page-size="pageSizeValue"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 查看原文对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="`${title || '原文明细'} - 详情`"
      width="80%"
      :before-close="handleDialogClose"
      append-to-body
    >
      <div class="dialog-content">
        <div class="detail-header">
          <h4>{{ getContentTitle(selectedRow) }}</h4>
          <div class="detail-meta">
            <TextInfos :data="selectedRow" :text-type="getTextType(selectedRow.dataSourceName)" />
          </div>
        </div>

        <div class="detail-content">
          <div class="content-text">
            {{ getContentDetail(selectedRow) }}
          </div>
        </div>

        <div class="detail-keywords">
          <KeywordTags
            :index-type-name="indexTypeName"
            :data="selectedRow.analysisResult"
            :show-title="true"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import KeywordTags from './components/KeywordTags.vue'
import TextInfos from './components/TextInfos.vue'
import type {
  OriginalDetailsProps,
  OriginalDetailsEmits,
  OriginalTextItem,
  DataSourceTypeMap,
  DataSourceFieldMap
} from './types.d'

// 组件定义
defineOptions({
  name: 'OriginalDetails'
})

// Props
const props = withDefaults(defineProps<OriginalDetailsProps>(), {
  loading: false,
  pageSize: 10,
  currentPage: 1,
  indexTypeName: '全领域业务',
  title: '原文明细'
})

// Emits
const emit = defineEmits<OriginalDetailsEmits>()

// 响应式数据
const dialogVisible = ref(false)
const selectedRow = ref<OriginalTextItem>({} as OriginalTextItem)

// 分页数据
const currentPageValue = ref(props.currentPage)
const pageSizeValue = ref(props.pageSize)

// 数据源类型映射
const dataSourceTypeMap: DataSourceTypeMap = {
  '引力域-资讯': 'post_comments',
  '引力域-帖子': 'post_comments',
  联络中心热线服务: 'work_order',
  维修三包: 'work_order',
  口碑描述: 'feedback',
  '车机端-智慧小安': 'consulting_service',
  联络中心在线对话: 'consulting_service',
  GQRS: 'questionnaire',
  口碑评分: 'questionnaire',
  '爱卡汽车-用户发帖': 'post_comments',
  '百度贴吧-用户发帖': 'post_comments',
  '懂车帝-用户发帖': 'post_comments',
  '汽车之家-用户发帖': 'post_comments',
  '太平洋汽车-用户发帖': 'post_comments',
  '新浪微博-用户发帖': 'post_comments',
  '易车网-用户发帖': 'post_comments',
  长安汽车直评: 'questionnaire',
  集团智慧营销直评: 'questionnaire',
  凯程汽车直评: 'questionnaire',
  欧尚汽车直评: 'questionnaire',
  新能源汽车直评: 'questionnaire'
}

// 字段映射配置
const fieldMapping: DataSourceFieldMap = {
  post_comments: {
    title: 'postsTitle',
    content: 'postsContent',
    comment: 'comment'
  },
  work_order: {
    title: 'title',
    content: 'content'
  },
  feedback: {
    title: 'title',
    content: 'content'
  },
  consulting_service: {
    title: 'title',
    content: 'content'
  },
  questionnaire: {
    title: 'title',
    content: 'answer_content',
    answer_fraction: 'answer_fraction'
  }
}

// 监听Props变化
watch(
  () => props.currentPage,
  newVal => {
    currentPageValue.value = newVal
  }
)

watch(
  () => props.pageSize,
  newVal => {
    pageSizeValue.value = newVal
  }
)

// 获取数据源类型
const getTextType = (dataSourceName: string) => {
  return dataSourceTypeMap[dataSourceName] || 'post_comments'
}

// 获取内容标题
const getContentTitle = (row: OriginalTextItem) => {
  const type = getTextType(row.dataSourceName)
  const mapping = fieldMapping[type]

  return row[mapping.title!] || row[mapping.content!] || row[mapping.comment!] || '无标题'
}

// 获取内容详情
const getContentDetail = (row: OriginalTextItem) => {
  const type = getTextType(row.dataSourceName)
  const mapping = fieldMapping[type]

  return (
    row[mapping.content!] ||
    row[mapping.comment!] ||
    row[mapping.answer_content!] ||
    row[mapping.answer_fraction!] ||
    '无内容'
  )
}

// 获取表格索引
const getTableIndex = (index: number) => {
  return (currentPageValue.value - 1) * pageSizeValue.value + index + 1
}

// 获取行样式类名
const getRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSizeValue.value = size
  emit('page-change', { pageNum: 1, pageSize: size })
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPageValue.value = page
  emit('page-change', { pageNum: page })
}

// 处理查看用户详情
const handleUserDetail = (row: OriginalTextItem) => {
  if (row.oneId) {
    emit('user-detail', {
      oneId: row.oneId,
      userName: row.user || row.name || '未知用户'
    })
  }
}

// 处理查看原文详情
const handleViewDetail = (row: OriginalTextItem) => {
  selectedRow.value = row
  dialogVisible.value = true
}

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
  selectedRow.value = {} as OriginalTextItem
}
</script>

<style lang="scss" scoped>
.original-details {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16;
    padding: 16 0;

    .title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .data-count {
      font-size: 14px;
      color: #909399;
      font-weight: 500;
    }
  }

  .table-container {
    .content-cell {
      padding: 12px 0;

      .content-title {
        font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 1.5;
        margin-bottom: 8px;

        // 单行显示，超出省略
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .content-detail {
        font-size: 13px;
        color: #606266;
        line-height: 1.6;
        margin-bottom: 8px;
        word-break: break-all;

        // 最多显示2行
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      .no-user {
        color: #909399;
        font-size: 14px;
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 24px;
      padding: 16px 0;
    }
  }

  // 表格行样式
  :deep(.el-table) {
    .even-row {
      background-color: #fafafa;
    }

    .odd-row {
      background-color: #ffffff;
    }

    // 表格头部样式
    .el-table__header {
      th {
        background-color: #f2f6fc;
        color: #303133;
        font-weight: 600;
      }
    }

    // 表格行悬停效果
    .el-table__row:hover > td {
      background-color: rgba(#409eff, 0.04) !important;
    }
  }
}

// 对话框样式
.dialog-content {
  .detail-header {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #dcdfe6;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .detail-meta {
      margin-top: 8px;
    }
  }

  .detail-content {
    margin-bottom: 20px;

    .content-text {
      font-size: 14px;
      line-height: 1.8;
      color: #606266;
      background: #f5f5f5;
      padding: 16px;
      border-radius: 6px;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .detail-keywords {
    padding-top: 16px;
    border-top: 1px solid #dcdfe6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .original-details {
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .table-container {
      :deep(.el-table) {
        font-size: 12px;
      }

      .content-cell {
        .content-title {
          font-size: 13px;
        }

        .content-detail {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
