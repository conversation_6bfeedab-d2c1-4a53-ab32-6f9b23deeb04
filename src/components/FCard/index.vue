<script setup lang="ts">
defineOptions({
  name: 'FCard'
})

const {
  title = '标题',
  width = '100%',
  height = '100%'
} = defineProps<{
  title?: string
  width?: string
  height?: string
}>()
</script>

<template>
  <div class="f-card" :style="{ width, height }">
    <div class="fc-header">
      <div class="fch-left">
        <span class="text-h3 mr-8">{{ title }}</span>
        <el-icon :size="20" color="#999999"><Warning /></el-icon>
      </div>
      <div class="fch-right">
        <span class="text-body text-tertiary">查看更多</span>
        <el-icon :size="20" color="#999999"><ArrowRightBold /></el-icon>
      </div>
    </div>
    <div class="fc-body" :style="{ height: `calc(${height} - 48px)` }">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.f-card {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05);
  border-radius: 12px;
  .fc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 0;
    .fch-left {
      flex: 1;
      display: flex;
      align-items: center;
    }
    .fch-right {
      display: flex;
      align-items: center;
    }
  }
  .fc-body {
    padding: 24px;
  }
}
</style>
